#!/usr/bin/env python3
"""
多session_cgi_label调度示例脚本

这个脚本演示了如何使用修改后的调度器同时处理多个session_cgi_label
"""

import subprocess
import sys
import time
from datetime import datetime

def run_scheduler_with_multiple_labels():
    """运行调度器处理多个session_cgi_label"""
    
    # 示例1: 单次运行多个标签（异步模式）
    print("=== 示例1: 单次运行多个标签（异步模式）===")
    cmd = [
        "python", "scheduler_day_allapp_optimized.py",
        "--session_cgi_label=cgi7,cgi8,cgi9",  # 多个标签用逗号分隔
        "--run_once",                           # 只运行一次
        "--use_multiprocess",                   # 使用多进程
        "--max_workers=4",                      # 最大4个进程
        "--chunk_size=1000",                    # 每批处理1000行
        "--max_concurrent=5"                    # 最大并发5个任务
    ]
    
    print(f"执行命令: {' '.join(cmd)}")
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("STDOUT:")
        print(result.stdout)
        if result.stderr:
            print("STDERR:")
            print(result.stderr)
    except subprocess.CalledProcessError as e:
        print(f"命令执行失败: {e}")
        print(f"STDOUT: {e.stdout}")
        print(f"STDERR: {e.stderr}")
    
    print("\n" + "="*50 + "\n")
    
    # 示例2: 查看任务状态
    print("=== 示例2: 查看任务状态 ===")
    cmd_status = [
        "python", "scheduler_day_allapp_optimized.py",
        "--session_cgi_label=cgi7,cgi8,cgi9",
        "--status"  # 只显示状态
    ]
    
    print(f"执行命令: {' '.join(cmd_status)}")
    try:
        result = subprocess.run(cmd_status, check=True, capture_output=True, text=True)
        print("任务状态:")
        print(result.stdout)
    except subprocess.CalledProcessError as e:
        print(f"命令执行失败: {e}")
        print(f"STDOUT: {e.stdout}")
        print(f"STDERR: {e.stderr}")
    
    print("\n" + "="*50 + "\n")
    
    # 示例3: 同步模式运行（依次执行）
    print("=== 示例3: 同步模式运行（依次执行）===")
    cmd_sync = [
        "python", "scheduler_day_allapp_optimized.py",
        "--session_cgi_label=cgi7,cgi8",       # 只运行两个标签作为演示
        "--run_once",
        "--sync_mode",                          # 同步模式，依次执行
        "--use_multiprocess",
        "--max_workers=2",
        "--chunk_size=500"
    ]
    
    print(f"执行命令: {' '.join(cmd_sync)}")
    print("注意：同步模式会依次执行每个标签，可能需要较长时间...")
    
    # 这里只显示命令，不实际执行，因为可能耗时很长
    print("（为了演示，这里不实际执行同步模式）")

def demonstrate_scheduler_features():
    """演示调度器的各种功能"""
    
    print("=== 调度器功能演示 ===\n")
    
    features = [
        {
            "name": "多标签异步执行",
            "description": "同时启动多个session_cgi_label的任务，并发执行",
            "command": "--session_cgi_label=cgi7,cgi8,cgi9 --run_once --max_concurrent=5"
        },
        {
            "name": "多标签同步执行", 
            "description": "依次执行每个session_cgi_label的任务",
            "command": "--session_cgi_label=cgi7,cgi8,cgi9 --run_once --sync_mode"
        },
        {
            "name": "定时任务模式",
            "description": "在指定时间范围内定时执行多个标签的任务",
            "command": "--session_cgi_label=cgi7,cgi8 --start_time=00:00 --end_time=00:10 --interval=60"
        },
        {
            "name": "任务状态查看",
            "description": "查看所有标签的任务运行状态和统计信息",
            "command": "--session_cgi_label=cgi7,cgi8,cgi9 --status"
        },
        {
            "name": "性能优化配置",
            "description": "使用多进程和自定义参数优化性能",
            "command": "--session_cgi_label=cgi7,cgi8 --use_multiprocess --max_workers=6 --chunk_size=2000"
        }
    ]
    
    for i, feature in enumerate(features, 1):
        print(f"{i}. {feature['name']}")
        print(f"   描述: {feature['description']}")
        print(f"   命令: python scheduler_day_allapp_optimized.py {feature['command']}")
        print()

def show_usage_examples():
    """显示使用示例"""
    
    print("=== 使用示例 ===\n")
    
    examples = [
        {
            "scenario": "开发测试",
            "description": "快速测试单个或多个标签",
            "commands": [
                "# 测试单个标签",
                "python scheduler_day_allapp_optimized.py --session_cgi_label=cgi7 --run_once",
                "",
                "# 测试多个标签",
                "python scheduler_day_allapp_optimized.py --session_cgi_label=cgi7,cgi8 --run_once"
            ]
        },
        {
            "scenario": "生产环境",
            "description": "定时执行多个标签的任务",
            "commands": [
                "# 每天凌晨执行多个标签的任务",
                "python scheduler_day_allapp_optimized.py \\",
                "    --session_cgi_label=cgi7,cgi8,cgi9,cgi10 \\",
                "    --start_time=00:00 \\",
                "    --end_time=00:30 \\",
                "    --use_multiprocess \\",
                "    --max_workers=8 \\",
                "    --max_concurrent=6"
            ]
        },
        {
            "scenario": "监控运维",
            "description": "监控任务状态和性能",
            "commands": [
                "# 查看当前任务状态",
                "python scheduler_day_allapp_optimized.py --session_cgi_label=cgi7,cgi8,cgi9 --status",
                "",
                "# 查看日志文件",
                "ls -la logs/",
                "tail -f logs/cgi7_20240730.log"
            ]
        }
    ]
    
    for example in examples:
        print(f"场景: {example['scenario']}")
        print(f"描述: {example['description']}")
        print("命令:")
        for cmd in example['commands']:
            print(f"  {cmd}")
        print()

if __name__ == "__main__":
    print("多session_cgi_label调度器使用演示")
    print("=" * 50)
    print()
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "run":
            run_scheduler_with_multiple_labels()
        elif sys.argv[1] == "features":
            demonstrate_scheduler_features()
        elif sys.argv[1] == "examples":
            show_usage_examples()
        else:
            print("未知参数，请使用: run, features, 或 examples")
    else:
        print("使用方法:")
        print("  python multi_session_example.py run       # 运行示例")
        print("  python multi_session_example.py features  # 显示功能说明")
        print("  python multi_session_example.py examples  # 显示使用示例")
        print()
        
        # 默认显示功能说明
        demonstrate_scheduler_features()
